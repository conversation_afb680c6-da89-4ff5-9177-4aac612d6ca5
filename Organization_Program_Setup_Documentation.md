# Organization Program Setup Service

## Overview

The Organization Program Setup service allows organizations to define and manage their own programs. Each program is identified by an `orgId`, `programName`, and unique `programKey`. Program details are stored in a flexible JSONB structure for customization without schema changes.

## API Endpoints

### Create Program
```
POST /orgs/{orgId}/programs
```

**Request Body:**
```json
{
  "programName": "Long-Term Services and Supports",
  "programKey": "ltss",
  "programConfig": {
    "orgId": "org_roseman_genesis",
    "templateId": "ltss_template_001",
    "programType": "LTSS",
    "displayName": "Long-Term Services and Supports",
    "description": "This program supports individuals with long-term care needs...",
    "reviewFrequencyDays": 90,
    "daysToCompleteAssessment": 30,
    "requiredAssessments": [
      {
        "itemKey": "adl_iadl",
        "title": "ADL/IADL Functional Assessment",
        "required": true
      },
      {
        "itemKey": "sdoh",
        "title": "Social Determinants of Health (SDOH)",
        "required": true
      }
    ],
    "programReviews": [
      {
        "itemKey": "program_review",
        "title": "Quarterly Program Review",
        "required": true
      }
    ]
  }
}
```

### Get All Programs for Organization
```
GET /orgs/{orgId}/programs
```

### Get Single Program
```
GET /orgs/{orgId}/programs/{programKey}
```

### Update Program
```
PUT /orgs/{orgId}/programs/{programKey}
```

**Request Body:**
```json
{
  "programName": "Updated Program Name",
  "programConfig": {
    // Updated configuration object
  }
}
```

### Delete Program
```
DELETE /orgs/{orgId}/programs/{programKey}
```

## Database Schema

### Table: org_programs

| Column | Type | Constraints |
|--------|------|-------------|
| id | UUID | Primary Key |
| org_id | text | Not null, indexed |
| program_name | text | Not null |
| program_key | text | Not null |
| program_config | jsonb | Not null |
| created_at | timestamptz | Default now() |
| updated_at | timestamptz | Default now() |

**Unique Constraint:** (org_id, program_key)

## Example Usage

### 1. Create a new LTSS program
```bash
curl -X POST http://localhost:8080/orgs/org_roseman_genesis/programs \
  -H "Content-Type: application/json" \
  -d '{
    "programName": "Long-Term Services and Supports",
    "programKey": "ltss",
    "programConfig": {
      "orgId": "org_roseman_genesis",
      "templateId": "ltss_template_001",
      "programType": "LTSS",
      "displayName": "Long-Term Services and Supports",
      "description": "This program supports individuals with long-term care needs",
      "reviewFrequencyDays": 90,
      "daysToCompleteAssessment": 30,
      "requiredAssessments": [
        {
          "itemKey": "adl_iadl",
          "title": "ADL/IADL Functional Assessment",
          "required": true
        }
      ],
      "programReviews": [
        {
          "itemKey": "program_review",
          "title": "Quarterly Program Review",
          "required": true
        }
      ]
    }
  }'
```

### 2. Get all programs for an organization
```bash
curl http://localhost:8080/orgs/org_roseman_genesis/programs
```

### 3. Get a specific program
```bash
curl http://localhost:8080/orgs/org_roseman_genesis/programs/ltss
```

## Migration

To apply the database changes, run:

```bash
swift run App migrate
```

The migration will create the `org_programs` table with the required schema and constraints.

## Features

- ✅ Flexible JSONB configuration storage
- ✅ Unique program keys per organization
- ✅ Full CRUD operations
- ✅ RESTful API design
- ✅ Proper error handling
- ✅ Database constraints for data integrity

## Error Responses

- `400 Bad Request` - Invalid organization ID or program key
- `404 Not Found` - Program not found
- `409 Conflict` - Program key already exists for organization
- `422 Unprocessable Entity` - Invalid request body

## Next Steps

1. Run the migration: `swift run App migrate`
2. Test the endpoints with the provided examples
3. Integrate with your frontend application
4. Add authentication/authorization as needed
